import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const updateCabinetSchema = z.object({
  name: z.string().min(1).optional(),
  number: z.string().min(1).optional(),
  capacity: z.number().min(1).max(100).optional(),
  floor: z.number().optional(),
  building: z.string().optional(),
  branch: z.string().optional(),
  equipment: z.string().optional(),
  notes: z.string().optional(),
  isActive: z.boolean().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const cabinet = await prisma.cabinet.findUnique({
      where: { id },
      include: {
        groups: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            _count: {
              select: {
                enrollments: true,
              },
            },
          },
        },
        schedules: {
          include: {
            group: {
              include: {
                course: {
                  select: {
                    name: true,
                    level: true,
                  },
                },
                teacher: {
                  include: {
                    user: {
                      select: {
                        name: true,
                      },
                    },
                  },
                },
              },
            },
          },
          orderBy: [
            { dayOfWeek: 'asc' },
            { startTime: 'asc' },
          ],
        },
        _count: {
          select: {
            groups: true,
            schedules: true,
          },
        },
      },
    })

    if (!cabinet) {
      return NextResponse.json(
        { error: 'Cabinet not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(cabinet)
  } catch (error) {
    console.error('Error fetching cabinet:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to update cabinets
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = updateCabinetSchema.parse(body)

    // Check if cabinet exists
    const existingCabinet = await prisma.cabinet.findUnique({
      where: { id },
    })

    if (!existingCabinet) {
      return NextResponse.json(
        { error: 'Cabinet not found' },
        { status: 404 }
      )
    }

    // Check if cabinet number already exists in the same branch (if updating number or branch)
    if (validatedData.number || validatedData.branch) {
      const checkNumber = validatedData.number || existingCabinet.number
      const checkBranch = validatedData.branch || existingCabinet.branch

      const duplicateCabinet = await prisma.cabinet.findFirst({
        where: {
          number: checkNumber,
          branch: checkBranch,
          id: { not: id },
        },
      })

      if (duplicateCabinet) {
        return NextResponse.json(
          { error: 'Cabinet number already exists in this branch' },
          { status: 400 }
        )
      }
    }

    const cabinet = await prisma.cabinet.update({
      where: { id },
      data: validatedData,
      include: {
        groups: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
        _count: {
          select: {
            groups: true,
            schedules: true,
          },
        },
      },
    })

    // Log activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: (session.user.role as Role) || Role.ADMIN,
      action: 'UPDATE',
      resource: 'CABINET',
      resourceId: cabinet.id,
      details: `Updated cabinet ${cabinet.name} (${cabinet.number})`,
    })

    return NextResponse.json(cabinet)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating cabinet:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to delete cabinets
    if (!session.user.role || !['ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { id } = await params

    // Check if cabinet exists and has groups
    const existingCabinet = await prisma.cabinet.findUnique({
      where: { id },
      include: {
        groups: true,
        schedules: true,
      },
    })

    if (!existingCabinet) {
      return NextResponse.json(
        { error: 'Cabinet not found' },
        { status: 404 }
      )
    }

    // Check if cabinet has active groups
    if (existingCabinet.groups.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete cabinet with assigned groups',
          details: `Cabinet has ${existingCabinet.groups.length} group(s). Please reassign groups first.`
        },
        { status: 400 }
      )
    }

    // Delete cabinet (schedules will be deleted automatically due to cascade)
    await prisma.cabinet.delete({
      where: { id },
    })

    // Log activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: (session.user.role as Role) || Role.ADMIN,
      action: 'DELETE',
      resource: 'CABINET',
      resourceId: id,
      details: `Deleted cabinet ${existingCabinet.name} (${existingCabinet.number})`,
    })

    return NextResponse.json({ message: 'Cabinet deleted successfully' })
  } catch (error) {
    console.error('Error deleting cabinet:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
