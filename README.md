# Innovative Centre CRM System

A comprehensive Customer Relationship Management (CRM) system built for Innovative Centre, an English language center in Uzbekistan with 4,000+ students across 2 branches.

## 🚀 Features

### Core Functionality
- **Landing Page**: Modern, responsive homepage with lead capture form
- **Lead Management**: Track and manage potential student inquiries
- **Student Management**: Comprehensive student profiles and academic tracking
- **Course Management**: Manage courses from General English to IELTS preparation
- **Group Management**: Organize students into classes with capacity limits
- **Payment Processing**: Handle payments with multiple methods (Cash, UzCard, Humo, Payme, Click)
- **Role-Based Access**: Different dashboards for Admin, Manager, Teacher, Reception, Student, Parent
- **Real-time Analytics**: Dashboard with key metrics and insights

### Technical Features
- **Next.js 14** with App Router for modern React development
- **TypeScript** for type safety
- **PostgreSQL** database with Prisma ORM
- **NextAuth.js v5** for authentication
- **Tailwind CSS + shadcn/ui** for beautiful, responsive UI
- **Vercel-ready** deployment configuration

### Advanced Features (Phase 6)
- **SMS Integration**: Uzbek SMS providers (Eskiz.uz, SMS.uz, Playmobile)
- **Email Notifications**: Automated email campaigns with templates
- **Automated Workflows**: Business process automation with triggers
- **Performance Optimization**: Caching and query optimization
- **Comprehensive Analytics**: Revenue, enrollment, attendance, progress charts
- **Report Generation**: CSV/PDF export capabilities
- **Multi-channel Communication**: SMS, Email, and Push notifications

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js v5
- **UI**: Tailwind CSS + shadcn/ui components
- **State Management**: Zustand + React Query
- **Deployment**: Vercel optimized
- **SMS**: Eskiz.uz, SMS.uz, Playmobile integration
- **Email**: Nodemailer with Gmail/Outlook/SMTP support
- **Charts**: Recharts for analytics visualization
- **Forms**: React Hook Form + Zod validation

## 📋 Prerequisites

- Node.js 18+
- npm or yarn
- PostgreSQL database (provided)
- SMS provider account (Eskiz.uz recommended)
- Email service (Gmail with App Password recommended)

## 🚀 Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd inno-crm
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```
   Update the `.env.local` file with your database credentials.

4. **Set up the database**
   ```bash
   npm run db:push
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to `http://localhost:3000`

## 🔐 Default Login Credentials

After running the seed script, you can log in with these default accounts:

- **Admin**: Phone: `+************`, Password: `admin123`
- **Manager**: Phone: `+************`, Password: `manager123`
- **Reception**: Phone: `+************`, Password: `reception123`

## 📁 Project Structure

```
inno-crm/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   ├── (dashboard)/       # Protected dashboard routes
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            # Reusable UI components
│   ├── ui/               # shadcn/ui components
│   ├── forms/            # Form components
│   └── dashboard/        # Dashboard-specific components
├── lib/                   # Utilities and configurations
├── prisma/               # Database schema and migrations
├── types/                # TypeScript type definitions
└── public/               # Static assets
```

## 🎯 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:push` - Push database schema
- `npm run db:studio` - Open Prisma Studio
- `npm run db:generate` - Generate Prisma client
- `npm run db:seed` - Seed database with sample data

## 🌐 Deployment

This project is optimized for Vercel deployment:

1. **Connect to Vercel**
   - Import your repository to Vercel
   - Set environment variables in Vercel dashboard

2. **Environment Variables**
   ```
   DATABASE_URL=your_postgresql_connection_string
   NEXTAUTH_SECRET=your_secret_key
   NEXTAUTH_URL=your_production_url
   ```

3. **Deploy**
   - Vercel will automatically deploy on every push to main branch

## 🏢 Business Context

**Innovative Centre** is a leading English language center in Uzbekistan with:
- 4,000+ students across 2 branches
- Courses: General English, IELTS, SAT, Math, Kids English
- Age groups: Kids to adults
- Class formats: Group (12-30 students), Individual, Online
- Languages: English/Uzbek interface support
- Currency: UZS (Uzbek Som)

## 📊 Key Features by Role

### Admin Dashboard
- System overview and analytics
- User management
- Course and group management
- Financial reporting

### Manager Dashboard
- Operations metrics
- Staff performance tracking
- Revenue analytics
- Lead conversion reports

### Teacher Dashboard
- Class schedules and management
- Student attendance tracking
- Grade and progress management
- Communication tools

### Reception Dashboard
- Lead management and processing
- Student enrollment
- Payment processing
- Customer service tools

### Student/Parent Portal
- Academic progress tracking
- Schedule and assignments
- Payment history
- Direct communication with teachers

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary software developed for Innovative Centre.

## 📞 Support

For technical support or questions, please contact the development team.

---

Built with ❤️ for Innovative Centre using Next.js 14 and modern web technologies.
