"use client"

import { useState, useEffect, useCallback } from "react"
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from "recharts"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Users, TrendingUp, UserPlus, UserMinus } from "lucide-react"

interface EnrollmentData {
  month: string
  newEnrollments: number
  totalEnrollments: number
  dropouts: number
  completions: number
}

interface CourseEnrollmentData {
  course: string
  enrollments: number
  level: string
  percentage: number
}

interface EnrollmentChartProps {
  className?: string
}

export function EnrollmentChart({ className }: EnrollmentChartProps) {
  const [enrollmentData, setEnrollmentData] = useState<EnrollmentData[]>([])
  const [courseData, setCourseData] = useState<CourseEnrollmentData[]>([])
  const [chartType, setChartType] = useState<"line" | "area" | "bar">("area")
  const [timeRange, setTimeRange] = useState("12months")
  const [loading, setLoading] = useState(true)

  // Fetch enrollment analytics data
  const fetchEnrollmentData = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/analytics/enrollments?range=${timeRange}`)
      if (response.ok) {
        const data = await response.json()
        setEnrollmentData(data.monthlyEnrollments || [])
        setCourseData(data.courseEnrollments || [])
      }
    } catch (error) {
      console.error("Error fetching enrollment data:", error)
      // Mock data for development
      setEnrollmentData([
        { month: "Jan", newEnrollments: 45, totalEnrollments: 320, dropouts: 8, completions: 12 },
        { month: "Feb", newEnrollments: 52, totalEnrollments: 364, dropouts: 6, completions: 15 },
        { month: "Mar", newEnrollments: 38, totalEnrollments: 387, dropouts: 12, completions: 18 },
        { month: "Apr", newEnrollments: 61, totalEnrollments: 436, dropouts: 9, completions: 22 },
        { month: "May", newEnrollments: 48, totalEnrollments: 475, dropouts: 7, completions: 25 },
        { month: "Jun", newEnrollments: 67, totalEnrollments: 535, dropouts: 5, completions: 28 },
        { month: "Jul", newEnrollments: 55, totalEnrollments: 582, dropouts: 8, completions: 30 },
        { month: "Aug", newEnrollments: 72, totalEnrollments: 646, dropouts: 6, completions: 35 },
        { month: "Sep", newEnrollments: 68, totalEnrollments: 708, dropouts: 4, completions: 38 },
        { month: "Oct", newEnrollments: 74, totalEnrollments: 778, dropouts: 3, completions: 42 },
        { month: "Nov", newEnrollments: 71, totalEnrollments: 846, dropouts: 2, completions: 45 },
        { month: "Dec", newEnrollments: 78, totalEnrollments: 922, dropouts: 1, completions: 48 },
      ])
      setCourseData([
        { course: "General English A1", enrollments: 156, level: "A1", percentage: 25 },
        { course: "General English A2", enrollments: 134, level: "A2", percentage: 21 },
        { course: "General English B1", enrollments: 128, level: "B1", percentage: 20 },
        { course: "IELTS 6.0", enrollments: 89, level: "IELTS_6_0", percentage: 14 },
        { course: "General English B2", enrollments: 67, level: "B2", percentage: 11 },
        { course: "IELTS 6.5", enrollments: 45, level: "IELTS_6_5", percentage: 7 },
        { course: "Kids English", enrollments: 23, level: "KIDS", percentage: 4 },
      ])
    } finally {
      setLoading(false)
    }
  }, [timeRange])

  useEffect(() => {
    fetchEnrollmentData()
  }, [fetchEnrollmentData])

  // Calculate metrics
  const currentMonth = enrollmentData[enrollmentData.length - 1]
  const previousMonth = enrollmentData[enrollmentData.length - 2]
  
  const totalNewEnrollments = enrollmentData.reduce((sum, item) => sum + item.newEnrollments, 0)
  const totalDropouts = enrollmentData.reduce((sum, item) => sum + item.dropouts, 0)
  const totalCompletions = enrollmentData.reduce((sum, item) => sum + item.completions, 0)
  
  const growthRate = previousMonth && currentMonth
    ? ((currentMonth.newEnrollments - previousMonth.newEnrollments) / previousMonth.newEnrollments * 100).toFixed(1)
    : "0"

  const retentionRate = totalNewEnrollments > 0
    ? (((totalNewEnrollments - totalDropouts) / totalNewEnrollments) * 100).toFixed(1)
    : "0"

  // Colors for charts
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82CA9D", "#FFC658"]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-semibold">Enrollment Analytics</h3>
          <p className="text-sm text-gray-500">Track student enrollment trends and course popularity</p>
        </div>
        <div className="flex gap-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="6months">Last 6 Months</option>
            <option value="12months">Last 12 Months</option>
            <option value="24months">Last 24 Months</option>
          </select>
          <Button
            variant={chartType === "area" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("area")}
          >
            Area
          </Button>
          <Button
            variant={chartType === "line" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("line")}
          >
            Line
          </Button>
          <Button
            variant={chartType === "bar" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("bar")}
          >
            Bar
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Students</p>
              <p className="text-2xl font-bold">{currentMonth?.totalEnrollments || 0}</p>
            </div>
            <Users className="h-8 w-8 text-blue-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">New Enrollments</p>
              <p className="text-2xl font-bold">{totalNewEnrollments}</p>
            </div>
            <UserPlus className="h-8 w-8 text-green-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Growth Rate</p>
              <p className="text-2xl font-bold text-green-600">+{growthRate}%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Retention Rate</p>
              <p className="text-2xl font-bold text-purple-600">{retentionRate}%</p>
            </div>
            <UserMinus className="h-8 w-8 text-purple-600" />
          </div>
        </Card>
      </div>

      {/* Enrollment Trend Chart */}
      <Card className="p-6">
        <h4 className="text-lg font-medium mb-4">Enrollment Trends</h4>
        {loading ? (
          <div className="h-80 flex items-center justify-center">
            <p className="text-gray-500">Loading chart data...</p>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height={400}>
            {chartType === "area" ? (
              <AreaChart data={enrollmentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="totalEnrollments"
                  stackId="1"
                  stroke="#0088FE"
                  fill="#0088FE"
                  fillOpacity={0.6}
                  name="Total Enrollments"
                />
                <Area
                  type="monotone"
                  dataKey="newEnrollments"
                  stackId="2"
                  stroke="#00C49F"
                  fill="#00C49F"
                  fillOpacity={0.6}
                  name="New Enrollments"
                />
              </AreaChart>
            ) : chartType === "line" ? (
              <LineChart data={enrollmentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="totalEnrollments"
                  stroke="#0088FE"
                  strokeWidth={3}
                  dot={{ fill: "#0088FE", strokeWidth: 2, r: 4 }}
                  name="Total Enrollments"
                />
                <Line
                  type="monotone"
                  dataKey="newEnrollments"
                  stroke="#00C49F"
                  strokeWidth={2}
                  dot={{ fill: "#00C49F", strokeWidth: 2, r: 3 }}
                  name="New Enrollments"
                />
                <Line
                  type="monotone"
                  dataKey="dropouts"
                  stroke="#FF8042"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={{ fill: "#FF8042", strokeWidth: 2, r: 3 }}
                  name="Dropouts"
                />
              </LineChart>
            ) : (
              <BarChart data={enrollmentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="newEnrollments" fill="#00C49F" name="New Enrollments" />
                <Bar dataKey="dropouts" fill="#FF8042" name="Dropouts" />
                <Bar dataKey="completions" fill="#FFBB28" name="Completions" />
              </BarChart>
            )}
          </ResponsiveContainer>
        )}
      </Card>

      {/* Course Popularity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h4 className="text-lg font-medium mb-4">Course Popularity</h4>
          {loading ? (
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">Loading chart data...</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={courseData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ course, percentage }) => `${percentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="enrollments"
                >
                  {courseData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number, name: string) => [value, "Enrollments"]}
                />
              </PieChart>
            </ResponsiveContainer>
          )}
        </Card>

        <Card className="p-6">
          <h4 className="text-lg font-medium mb-4">Course Breakdown</h4>
          <div className="space-y-4">
            {courseData.map((course, index) => (
              <div key={course.course} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  />
                  <div>
                    <div className="font-medium">{course.course}</div>
                    <div className="text-sm text-gray-500">Level: {course.level}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{course.enrollments}</div>
                  <div className="text-sm text-gray-500">{course.percentage}%</div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Monthly Breakdown */}
      <Card className="p-6">
        <h4 className="text-lg font-medium mb-4">Monthly Breakdown</h4>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2">Month</th>
                <th className="text-right py-2">New</th>
                <th className="text-right py-2">Total</th>
                <th className="text-right py-2">Dropouts</th>
                <th className="text-right py-2">Completions</th>
                <th className="text-right py-2">Net Growth</th>
              </tr>
            </thead>
            <tbody>
              {enrollmentData.map((month) => {
                const netGrowth = month.newEnrollments - month.dropouts
                return (
                  <tr key={month.month} className="border-b">
                    <td className="py-2 font-medium">{month.month}</td>
                    <td className="text-right py-2 text-green-600">+{month.newEnrollments}</td>
                    <td className="text-right py-2">{month.totalEnrollments}</td>
                    <td className="text-right py-2 text-red-600">-{month.dropouts}</td>
                    <td className="text-right py-2 text-blue-600">{month.completions}</td>
                    <td className={`text-right py-2 font-medium ${netGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {netGrowth >= 0 ? '+' : ''}{netGrowth}
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  )
}
