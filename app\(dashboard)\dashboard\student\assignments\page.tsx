import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  FileText, 
  Upload, 
  Calendar,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
  Download,
  BookOpen
} from 'lucide-react'

export default function StudentAssignmentsPage() {
  // Mock data - in real implementation, this would come from API
  const assignmentData = {
    pending: [
      {
        id: 1,
        title: "Essay: My Dream Vacation",
        course: "General English B1",
        dueDate: "2024-01-20",
        description: "Write a 300-word essay about your dream vacation destination",
        type: "Essay",
        maxScore: 100,
        status: "pending",
        priority: "high"
      },
      {
        id: 2,
        title: "Vocabulary Quiz: Unit 6",
        course: "General English B1",
        dueDate: "2024-01-24",
        description: "Complete the vocabulary quiz covering travel and tourism terms",
        type: "Quiz",
        maxScore: 50,
        status: "pending",
        priority: "medium"
      }
    ],
    submitted: [
      {
        id: 3,
        title: "Grammar Exercise: Present Perfect",
        course: "General English B1",
        submittedDate: "2024-01-12",
        dueDate: "2024-01-15",
        score: 85,
        maxScore: 100,
        feedback: "Good understanding of present perfect tense. Work on irregular verbs.",
        status: "graded",
        type: "Exercise"
      },
      {
        id: 4,
        title: "Speaking Recording: Describing Places",
        course: "General English B1",
        submittedDate: "2024-01-10",
        dueDate: "2024-01-12",
        score: 78,
        maxScore: 100,
        feedback: "Clear pronunciation. Try to use more descriptive adjectives.",
        status: "graded",
        type: "Speaking"
      }
    ],
    upcoming: [
      {
        id: 5,
        title: "Reading Comprehension Test",
        course: "General English B1",
        assignDate: "2024-01-25",
        dueDate: "2024-01-30",
        description: "Test on reading comprehension skills",
        type: "Test",
        maxScore: 100,
        status: "upcoming"
      }
    ]
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-orange-100 text-orange-800'
      case 'submitted':
        return 'bg-blue-100 text-blue-800'
      case 'graded':
        return 'bg-green-100 text-green-800'
      case 'upcoming':
        return 'bg-gray-100 text-gray-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'submitted':
        return <Upload className="h-4 w-4" />
      case 'graded':
        return <CheckCircle className="h-4 w-4" />
      case 'upcoming':
        return <Calendar className="h-4 w-4" />
      case 'overdue':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100
    if (percentage >= 80) return "text-green-600"
    if (percentage >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">My Assignments</h1>
        <p className="text-gray-600">Track and submit your course assignments</p>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assignmentData.pending.length}</div>
            <p className="text-xs text-muted-foreground">Due soon</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Submitted</CardTitle>
            <Upload className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assignmentData.submitted.length}</div>
            <p className="text-xs text-muted-foreground">Awaiting grades</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Graded</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assignmentData.submitted.filter(a => a.status === 'graded').length}</div>
            <p className="text-xs text-muted-foreground">Completed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
            <Calendar className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assignmentData.upcoming.length}</div>
            <p className="text-xs text-muted-foreground">Future assignments</p>
          </CardContent>
        </Card>
      </div>

      {/* Pending Assignments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2 text-orange-600" />
            Pending Assignments
          </CardTitle>
          <CardDescription>Assignments that need to be completed</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {assignmentData.pending.map((assignment) => (
              <div key={assignment.id} className="flex items-center justify-between p-4 border rounded-lg bg-orange-50">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full">
                    {getStatusIcon(assignment.status)}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">{assignment.title}</h4>
                    <p className="text-sm text-gray-600">{assignment.course}</p>
                    <p className="text-sm text-gray-500 mt-1">{assignment.description}</p>
                    <div className="flex items-center space-x-4 mt-2">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">Due: {assignment.dueDate}</span>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {assignment.type}
                      </Badge>
                      <Badge className={getPriorityColor(assignment.priority)}>
                        {assignment.priority} priority
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">{assignment.maxScore} pts</span>
                  <Button size="sm">
                    <Upload className="h-4 w-4 mr-1" />
                    Submit
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Submitted/Graded Assignments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
            Submitted Assignments
          </CardTitle>
          <CardDescription>Your completed assignments and grades</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {assignmentData.submitted.map((assignment) => (
              <div key={assignment.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full">
                    {getStatusIcon(assignment.status)}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">{assignment.title}</h4>
                    <p className="text-sm text-gray-600">{assignment.course}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">Submitted: {assignment.submittedDate}</span>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {assignment.type}
                      </Badge>
                    </div>
                    {assignment.feedback && (
                      <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                        <strong>Feedback:</strong> {assignment.feedback}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className={`text-lg font-bold ${getScoreColor(assignment.score!, assignment.maxScore)}`}>
                      {assignment.score}/{assignment.maxScore}
                    </div>
                    <div className="text-sm text-gray-500">
                      {Math.round((assignment.score! / assignment.maxScore) * 100)}%
                    </div>
                  </div>
                  <Badge className={getStatusColor(assignment.status)}>
                    {assignment.status}
                  </Badge>
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Upcoming Assignments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-gray-600" />
            Upcoming Assignments
          </CardTitle>
          <CardDescription>Future assignments to be released</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {assignmentData.upcoming.map((assignment) => (
              <div key={assignment.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full">
                    {getStatusIcon(assignment.status)}
                  </div>
                  <div>
                    <h4 className="font-medium">{assignment.title}</h4>
                    <p className="text-sm text-gray-600">{assignment.course}</p>
                    <p className="text-sm text-gray-500 mt-1">{assignment.description}</p>
                    <div className="flex items-center space-x-4 mt-2">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">Available: {assignment.assignDate}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">Due: {assignment.dueDate}</span>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {assignment.type}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">{assignment.maxScore} pts</span>
                  <Badge className={getStatusColor(assignment.status)}>
                    {assignment.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-16 flex flex-col space-y-1">
              <Upload className="h-5 w-5" />
              <span className="text-sm">Submit Assignment</span>
            </Button>
            <Button variant="outline" className="h-16 flex flex-col space-y-1">
              <Download className="h-5 w-5" />
              <span className="text-sm">Download Materials</span>
            </Button>
            <Button variant="outline" className="h-16 flex flex-col space-y-1">
              <BookOpen className="h-5 w-5" />
              <span className="text-sm">Study Resources</span>
            </Button>
            <Button variant="outline" className="h-16 flex flex-col space-y-1">
              <Calendar className="h-5 w-5" />
              <span className="text-sm">Assignment Calendar</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
