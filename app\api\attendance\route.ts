import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const attendanceSchema = z.object({
  studentId: z.string(),
  classId: z.string(),
  status: z.enum(['PRESENT', 'ABSENT', 'LATE', 'EXCUSED']).default('PRESENT'),
  notes: z.string().optional(),
})

const bulkAttendanceSchema = z.object({
  classId: z.string(),
  attendances: z.array(z.object({
    studentId: z.string(),
    status: z.enum(['PRESENT', 'ABSENT', 'LATE', 'EXCUSED']),
    notes: z.string().optional(),
  })),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const classId = searchParams.get('classId')
    const studentId = searchParams.get('studentId')
    const groupId = searchParams.get('groupId')
    const teacherId = searchParams.get('teacherId')
    const status = searchParams.get('status')
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')

    const where: any = {}

    if (classId) {
      where.classId = classId
    }

    if (studentId) {
      where.studentId = studentId
    }

    if (groupId) {
      where.class = {
        ...where.class,
        groupId: groupId,
      }
    }

    if (teacherId) {
      // Find teacher record first
      const teacher = await prisma.teacher.findUnique({
        where: { userId: teacherId },
        select: { id: true }
      })

      if (teacher) {
        where.class = {
          ...where.class,
          teacherId: teacher.id,
        }
      }
    }

    if (status) {
      where.status = status
    }

    if (dateFrom || dateTo) {
      where.class = {
        ...where.class,
        date: {
          ...(dateFrom && { gte: new Date(dateFrom) }),
          ...(dateTo && { lte: new Date(dateTo) }),
        },
      }
    }

    const [attendances, total] = await Promise.all([
      prisma.attendance.findMany({
        where,
        include: {
          student: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  phone: true,
                },
              },
            },
          },
          class: {
            include: {
              group: {
                select: {
                  id: true,
                  name: true,
                },
              },
              teacher: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: [
          { class: { date: 'desc' } },
          { createdAt: 'desc' },
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.attendance.count({ where }),
    ])

    return NextResponse.json({
      attendances,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching attendance:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Check if this is bulk attendance or single attendance
    if (body.attendances && Array.isArray(body.attendances)) {
      // Bulk attendance creation
      const validatedData = bulkAttendanceSchema.parse(body)

      // Check if class exists
      const classExists = await prisma.class.findUnique({
        where: { id: validatedData.classId },
        include: {
          group: {
            include: {
              enrollments: {
                where: { status: 'ACTIVE' },
                select: { studentId: true },
              },
            },
          },
        },
      })

      if (!classExists) {
        return NextResponse.json(
          { error: 'Class not found' },
          { status: 400 }
        )
      }

      // Validate all students are enrolled in the group
      const enrolledStudentIds = classExists.group.enrollments.map(e => e.studentId)
      const invalidStudents = validatedData.attendances.filter(
        att => !enrolledStudentIds.includes(att.studentId)
      )

      if (invalidStudents.length > 0) {
        return NextResponse.json(
          { 
            error: 'Some students are not enrolled in this group',
            invalidStudents: invalidStudents.map(s => s.studentId)
          },
          { status: 400 }
        )
      }

      // Create attendance records
      const attendanceRecords = await Promise.all(
        validatedData.attendances.map(attendance =>
          prisma.attendance.upsert({
            where: {
              studentId_classId: {
                studentId: attendance.studentId,
                classId: validatedData.classId,
              },
            },
            update: {
              status: attendance.status,
              notes: attendance.notes,
              updatedAt: new Date(),
            },
            create: {
              studentId: attendance.studentId,
              classId: validatedData.classId,
              status: attendance.status,
              notes: attendance.notes,
            },
            include: {
              student: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          })
        )
      )

      return NextResponse.json({
        message: 'Bulk attendance recorded successfully',
        attendances: attendanceRecords,
      }, { status: 201 })

    } else {
      // Single attendance creation
      const validatedData = attendanceSchema.parse(body)

      // Check if student exists and is enrolled in the class's group
      const enrollment = await prisma.enrollment.findFirst({
        where: {
          studentId: validatedData.studentId,
          status: 'ACTIVE',
          group: {
            classes: {
              some: {
                id: validatedData.classId,
              },
            },
          },
        },
      })

      if (!enrollment) {
        return NextResponse.json(
          { error: 'Student is not enrolled in this class group' },
          { status: 400 }
        )
      }

      // Check if attendance already exists
      const existingAttendance = await prisma.attendance.findUnique({
        where: {
          studentId_classId: {
            studentId: validatedData.studentId,
            classId: validatedData.classId,
          },
        },
      })

      if (existingAttendance) {
        return NextResponse.json(
          { error: 'Attendance already recorded for this student in this class' },
          { status: 400 }
        )
      }

      const attendance = await prisma.attendance.create({
        data: validatedData,
        include: {
          student: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  phone: true,
                },
              },
            },
          },
          class: {
            include: {
              group: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      })

      return NextResponse.json(attendance, { status: 201 })
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating attendance:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
