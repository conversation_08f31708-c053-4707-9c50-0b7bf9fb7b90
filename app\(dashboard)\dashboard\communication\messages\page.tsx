'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatDate } from '@/lib/utils'
import { Search, Plus, MessageSquare, Send, Users, User, Edit, Trash2, Loader2, Mail } from 'lucide-react'

interface Message {
  id: string
  subject: string
  content: string
  recipientType: 'INDIVIDUAL' | 'GROUP' | 'ALL_STUDENTS' | 'ALL_TEACHERS' | 'ALL_PARENTS'
  status: 'DRAFT' | 'SENT' | 'DELIVERED' | 'READ'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  createdAt: string
  sentAt?: string
  sender: {
    name: string
  }
  recipients?: {
    id: string
    name: string
    status: 'SENT' | 'DELIVERED' | 'READ'
  }[]
}

export default function MessagesPage() {
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [priorityFilter, setPriorityFilter] = useState('ALL')
  const [typeFilter, setTypeFilter] = useState('ALL')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingMessage, setEditingMessage] = useState<Message | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Form state
  const [formData, setFormData] = useState<{
    subject: string
    content: string
    recipientType: 'INDIVIDUAL' | 'GROUP' | 'ALL_STUDENTS' | 'ALL_TEACHERS' | 'ALL_PARENTS'
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
    recipients: string[]
  }>({
    subject: '',
    content: '',
    recipientType: 'ALL_STUDENTS',
    priority: 'MEDIUM',
    recipients: []
  })

  const fetchMessages = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        ...(statusFilter !== 'ALL' && { status: statusFilter }),
        ...(priorityFilter !== 'ALL' && { priority: priorityFilter }),
      })

      const response = await fetch(`/api/messages?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch messages')
      }

      const data = await response.json()
      setMessages(data.messages)
      setTotalPages(data.pagination.pages)
      setError(null)
    } catch (error) {
      console.error('Error fetching messages:', error)
      setError('Failed to fetch messages')
    } finally {
      setLoading(false)
    }
  }, [currentPage, statusFilter, priorityFilter])

  useEffect(() => {
    fetchMessages()
  }, [fetchMessages])

  const handleCreateMessage = async () => {
    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subject: formData.subject,
          content: formData.content,
          recipientType: formData.recipientType,
          priority: formData.priority,
          recipientIds: formData.recipients,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create message')
      }

      const newMessage = await response.json()
      setMessages(prev => [newMessage, ...prev])
      setIsCreateDialogOpen(false)
      resetForm()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSendMessage = async (messageId: string) => {
    try {
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, status: 'SENT' as const, sentAt: new Date().toISOString() }
          : msg
      ))
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to send message')
    }
  }

  const handleUpdateMessage = async (data: any) => {
    if (!editingMessage) return

    setIsSubmitting(true)
    setError(null)

    try {
      // Mock update
      setMessages(prev => prev.map(msg =>
        msg.id === editingMessage.id
          ? { ...msg, ...data, updatedAt: new Date().toISOString() }
          : msg
      ))

      setIsEditDialogOpen(false)
      setEditingMessage(null)
      resetForm()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeleteMessage = async (messageId: string) => {
    if (!confirm('Are you sure you want to delete this message?')) {
      return
    }

    try {
      setMessages(prev => prev.filter(msg => msg.id !== messageId))
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    }
  }

  const openEditDialog = (message: Message) => {
    setEditingMessage(message)
    setFormData({
      subject: message.subject,
      content: message.content,
      recipientType: message.recipientType,
      priority: message.priority,
      recipients: []
    })
    setIsEditDialogOpen(true)
  }

  const resetForm = () => {
    setFormData({
      subject: '',
      content: '',
      recipientType: 'ALL_STUDENTS',
      priority: 'MEDIUM',
      recipients: []
    })
  }

  const filteredMessages = messages.filter(message => {
    const matchesSearch = message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.content.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'ALL' || message.status === statusFilter
    const matchesType = typeFilter === 'ALL' || message.recipientType === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'bg-gray-100 text-gray-800'
      case 'SENT': return 'bg-blue-100 text-blue-800'
      case 'DELIVERED': return 'bg-green-100 text-green-800'
      case 'READ': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800'
      case 'HIGH': return 'bg-orange-100 text-orange-800'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800'
      case 'LOW': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading messages...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Messages</h1>
          <p className="text-gray-600">Send and manage communications</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Compose Message
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Compose New Message</DialogTitle>
              <DialogDescription>
                Create and send a message to students, teachers, or parents.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Subject *</label>
                <Input
                  value={formData.subject}
                  onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
                  placeholder="Enter message subject"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Message Content *</label>
                <Textarea
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="Enter your message"
                  rows={6}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Recipients</label>
                  <Select value={formData.recipientType} onValueChange={(value: any) => setFormData(prev => ({ ...prev, recipientType: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL_STUDENTS">All Students</SelectItem>
                      <SelectItem value="ALL_TEACHERS">All Teachers</SelectItem>
                      <SelectItem value="ALL_PARENTS">All Parents</SelectItem>
                      <SelectItem value="GROUP">Specific Group</SelectItem>
                      <SelectItem value="INDIVIDUAL">Individual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium">Priority</label>
                  <Select value={formData.priority} onValueChange={(value: any) => setFormData(prev => ({ ...prev, priority: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="LOW">Low</SelectItem>
                      <SelectItem value="MEDIUM">Medium</SelectItem>
                      <SelectItem value="HIGH">High</SelectItem>
                      <SelectItem value="URGENT">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button variant="outline" onClick={handleCreateMessage} disabled={isSubmitting || !formData.subject || !formData.content}>
                  Save as Draft
                </Button>
                <Button onClick={handleCreateMessage} disabled={isSubmitting || !formData.subject || !formData.content}>
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <Send className="mr-2 h-4 w-4" />
                  Send Message
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search messages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Status</SelectItem>
                <SelectItem value="DRAFT">Draft</SelectItem>
                <SelectItem value="SENT">Sent</SelectItem>
                <SelectItem value="DELIVERED">Delivered</SelectItem>
                <SelectItem value="READ">Read</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by recipient type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Types</SelectItem>
                <SelectItem value="ALL_STUDENTS">All Students</SelectItem>
                <SelectItem value="ALL_TEACHERS">All Teachers</SelectItem>
                <SelectItem value="ALL_PARENTS">All Parents</SelectItem>
                <SelectItem value="GROUP">Group</SelectItem>
                <SelectItem value="INDIVIDUAL">Individual</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Messages List */}
      <div className="space-y-4">
        {filteredMessages.map((message) => (
          <Card key={message.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <MessageSquare className="h-5 w-5 text-blue-600" />
                    <CardTitle className="text-lg">{message.subject}</CardTitle>
                    <Badge className={getStatusColor(message.status)}>
                      {message.status}
                    </Badge>
                    <Badge className={getPriorityColor(message.priority)}>
                      {message.priority}
                    </Badge>
                  </div>
                  <CardDescription className="text-sm text-gray-500">
                    To: {message.recipientType.replace('_', ' ')} • By {message.sender.name} • {formatDate(message.createdAt)}
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  {message.status === 'DRAFT' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSendMessage(message.id)}
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openEditDialog(message)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteMessage(message.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">{message.content}</p>
              {message.recipients && (
                <div className="border-t pt-4">
                  <p className="text-sm font-medium text-gray-600 mb-2">Recipients ({message.recipients.length}):</p>
                  <div className="flex flex-wrap gap-2">
                    {message.recipients.slice(0, 5).map((recipient) => (
                      <Badge key={recipient.id} variant="outline" className="text-xs">
                        {recipient.name} ({recipient.status})
                      </Badge>
                    ))}
                    {message.recipients.length > 5 && (
                      <Badge variant="outline" className="text-xs">
                        +{message.recipients.length - 5} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredMessages.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No messages found matching your criteria.</p>
          </CardContent>
        </Card>
      )}

      {/* Edit Message Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Message</DialogTitle>
            <DialogDescription>
              Update the message details.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Subject *</label>
              <Input
                value={formData.subject}
                onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
                placeholder="Enter message subject"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Message Content *</label>
              <Textarea
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Enter your message"
                rows={6}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Recipients</label>
                <Select value={formData.recipientType} onValueChange={(value: any) => setFormData(prev => ({ ...prev, recipientType: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL_STUDENTS">All Students</SelectItem>
                    <SelectItem value="ALL_TEACHERS">All Teachers</SelectItem>
                    <SelectItem value="ALL_PARENTS">All Parents</SelectItem>
                    <SelectItem value="GROUP">Specific Group</SelectItem>
                    <SelectItem value="INDIVIDUAL">Individual</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium">Priority</label>
                <Select value={formData.priority} onValueChange={(value: any) => setFormData(prev => ({ ...prev, priority: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LOW">Low</SelectItem>
                    <SelectItem value="MEDIUM">Medium</SelectItem>
                    <SelectItem value="HIGH">High</SelectItem>
                    <SelectItem value="URGENT">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateMessage} disabled={isSubmitting || !formData.subject || !formData.content}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Update Message
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
