const { PrismaClient } = require('@prisma/client');

async function migrateLeadStatus() {
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL || "postgresql://crm_owner:<EMAIL>/crm?sslmode=require"
      }
    }
  });

  try {
    console.log('Starting lead status migration...');

    // First, let's see what lead statuses exist
    const leads = await prisma.$queryRaw`
      SELECT status, COUNT(*) as count
      FROM leads
      GROUP BY status
    `;

    console.log('Current lead statuses:', leads);

    // Step 1: Add new enum values to the existing enum
    console.log('Adding new enum values...');
    try {
      await prisma.$executeRaw`
        ALTER TYPE "LeadStatus" ADD VALUE IF NOT EXISTS 'CALLING'
      `;
      await prisma.$executeRaw`
        ALTER TYPE "LeadStatus" ADD VALUE IF NOT EXISTS 'CALL_COMPLETED'
      `;
      await prisma.$executeRaw`
        ALTER TYPE "LeadStatus" ADD VALUE IF NOT EXISTS 'GROUP_ASSIGNED'
      `;
      await prisma.$executeRaw`
        ALTER TYPE "LeadStatus" ADD VALUE IF NOT EXISTS 'ARCHIVED'
      `;
      await prisma.$executeRaw`
        ALTER TYPE "LeadStatus" ADD VALUE IF NOT EXISTS 'NOT_INTERESTED'
      `;
      console.log('New enum values added successfully');
    } catch (enumError) {
      console.log('Enum values might already exist, continuing...');
    }

    // Step 2: Update old enum values to new ones
    console.log('Updating CONTACTED to CALL_COMPLETED...');
    await prisma.$executeRaw`
      UPDATE leads
      SET status = 'CALL_COMPLETED'
      WHERE status = 'CONTACTED'
    `;

    console.log('Updating INTERESTED to GROUP_ASSIGNED...');
    await prisma.$executeRaw`
      UPDATE leads
      SET status = 'GROUP_ASSIGNED'
      WHERE status = 'INTERESTED'
    `;

    console.log('Updating ENROLLED to ARCHIVED...');
    await prisma.$executeRaw`
      UPDATE leads
      SET status = 'ARCHIVED'
      WHERE status = 'ENROLLED'
    `;

    // Verify the migration
    const updatedLeads = await prisma.$queryRaw`
      SELECT status, COUNT(*) as count
      FROM leads
      GROUP BY status
    `;

    console.log('Updated lead statuses:', updatedLeads);
    console.log('Lead status migration completed successfully!');

  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  migrateLeadStatus()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateLeadStatus };
