import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get student information for current user
    const student = await prisma.student.findUnique({
      where: { userId: session.user.id },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
        currentGroup: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
          },
        },
        attendances: {
          include: {
            class: {
              select: {
                date: true,
                topic: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: 50,
        },
        assessments: {
          orderBy: { createdAt: 'desc' },
          take: 20,
        },
        payments: {
          where: { status: 'PAID' },
          orderBy: { paidDate: 'desc' },
        },
      },
    })

    if (!student) {
      return NextResponse.json({ error: 'Student profile not found' }, { status: 404 })
    }

    // Calculate progress metrics
    const totalClasses = student.attendances.length
    const attendedClasses = student.attendances.filter(a => a.status === 'PRESENT').length
    const attendanceRate = totalClasses > 0 ? (attendedClasses / totalClasses) * 100 : 0

    // Calculate assessment average
    const completedAssessments = student.assessments.filter(a => a.score !== null)
    const averageScore = completedAssessments.length > 0 
      ? completedAssessments.reduce((sum, a) => sum + (a.score || 0), 0) / completedAssessments.length
      : 0

    // Calculate level progress (mock calculation based on assessments and attendance)
    const levelProgress = Math.min(100, (attendanceRate * 0.4) + (averageScore * 0.6))

    // Get next level
    const levelOrder = ['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']
    const currentLevelIndex = levelOrder.indexOf(student.level)
    const nextLevel = currentLevelIndex >= 0 && currentLevelIndex < levelOrder.length - 1 
      ? levelOrder[currentLevelIndex + 1] 
      : null

    // Calculate skill breakdown based on assessments with skill-specific adjustments
    const skills = [
      { name: "Speaking", progress: Math.min(100, Math.max(0, averageScore * 0.9)), level: student.level },
      { name: "Listening", progress: Math.min(100, Math.max(0, averageScore * 1.1)), level: student.level },
      { name: "Reading", progress: Math.min(100, Math.max(0, averageScore * 1.05)), level: student.level },
      { name: "Writing", progress: Math.min(100, Math.max(0, averageScore * 0.85)), level: student.level },
      { name: "Grammar", progress: Math.min(100, Math.max(0, averageScore)), level: student.level },
      { name: "Vocabulary", progress: Math.min(100, Math.max(0, averageScore * 0.95)), level: student.level },
    ]

    // Get recent achievements (based on assessments and attendance)
    const achievements = []
    if (attendanceRate >= 95) {
      achievements.push({ name: "Perfect Attendance", date: new Date().toISOString(), icon: "🎯" })
    }
    if (averageScore >= 90) {
      achievements.push({ name: "Excellence Award", date: new Date().toISOString(), icon: "🏆" })
    }
    if (completedAssessments.length >= 10) {
      achievements.push({ name: "Assessment Master", date: new Date().toISOString(), icon: "📚" })
    }

    const progressData = {
      student: {
        name: student.user.name,
        email: student.user.email,
        level: student.level,
        nextLevel,
        branch: student.branch,
      },
      currentGroup: student.currentGroup ? {
        name: student.currentGroup.name,
        course: student.currentGroup.course.name,
        level: student.currentGroup.course.level,
      } : null,
      progress: {
        overall: Math.round(levelProgress),
        attendance: Math.round(attendanceRate),
        averageScore: Math.round(averageScore),
      },
      statistics: {
        totalClasses,
        attendedClasses,
        completedAssessments: completedAssessments.length,
        pendingAssessments: student.assessments.filter(a => a.score === null).length,
        totalPayments: student.payments.length,
        totalPaid: student.payments.reduce((sum, p) => sum + Number(p.amount), 0),
      },
      skills,
      achievements,
      recentActivity: {
        assessments: student.assessments.slice(0, 5).map(a => ({
          id: a.id,
          testName: a.testName,
          score: a.score,
          maxScore: a.maxScore,
          passed: a.passed,
          completedAt: a.completedAt,
        })),
        attendance: student.attendances.slice(0, 10).map(a => ({
          id: a.id,
          date: a.class.date,
          topic: a.class.topic,
          status: a.status,
        })),
      },
    }

    return NextResponse.json(progressData)
  } catch (error) {
    console.error('Error fetching student progress:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
