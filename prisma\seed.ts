import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 10)
  
  const adminUser = await prisma.user.upsert({
    where: { phone: '+998901234567' },
    update: {},
    create: {
      phone: '+998901234567',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'ADMIN',
      password: hashedPassword,
    },
  })

  // Create manager user
  const managerPassword = await bcrypt.hash('manager123', 10)
  
  const managerUser = await prisma.user.upsert({
    where: { phone: '+998901234568' },
    update: {},
    create: {
      phone: '+998901234568',
      name: 'Manager User',
      email: '<EMAIL>',
      role: 'MANAGER',
      password: managerPassword,
    },
  })

  // Create reception user
  const receptionPassword = await bcrypt.hash('reception123', 10)

  const receptionUser = await prisma.user.upsert({
    where: { phone: '+998901234569' },
    update: {},
    create: {
      phone: '+998901234569',
      name: 'Reception User',
      email: '<EMAIL>',
      role: 'RECEPTION',
      password: receptionPassword,
    },
  })

  // Create cashier user (NEW ROLE)
  const cashierPassword = await bcrypt.hash('cashier123', 10)

  const cashierUser = await prisma.user.upsert({
    where: { phone: '+998901234570' },
    update: {},
    create: {
      phone: '+998901234570',
      name: 'Cashier User',
      email: '<EMAIL>',
      role: 'CASHIER',
      password: cashierPassword,
    },
  })

  // Create some sample courses
  const courses = [
    {
      name: 'General English A1',
      level: 'A1' as const,
      description: 'Beginner level English course',
      duration: 12,
      price: 500000,
    },
    {
      name: 'General English A2',
      level: 'A2' as const,
      description: 'Elementary level English course',
      duration: 12,
      price: 550000,
    },
    {
      name: 'IELTS Preparation',
      level: 'IELTS' as const,
      description: 'IELTS preparation course',
      duration: 8,
      price: 800000,
    },
    {
      name: 'Kids English Beginners',
      level: 'KIDS' as const,
      description: 'English for children aged 6-10',
      duration: 16,
      price: 400000,
    },
  ]

  for (const course of courses) {
    await prisma.course.upsert({
      where: { name: course.name },
      update: {},
      create: course,
    })
  }

  // Create some sample leads
  const leads = [
    {
      name: 'Aziza Karimova',
      phone: '+998901111111',
      coursePreference: 'IELTS Preparation',
      status: 'NEW' as const,
      source: 'Website',
    },
    {
      name: 'Bobur Toshev',
      phone: '+998902222222',
      coursePreference: 'General English',
      status: 'CALL_COMPLETED' as const,
      source: 'Website',
    },
    {
      name: 'Dilnoza Rahimova',
      phone: '+998903333333',
      coursePreference: 'Kids English',
      status: 'GROUP_ASSIGNED' as const,
      source: 'Website',
    },
  ]

  for (const lead of leads) {
    await prisma.lead.upsert({
      where: { phone: lead.phone },
      update: {},
      create: lead,
    })
  }

  // Add call records with mock recording URLs for testing
  const createdLeads = await prisma.lead.findMany()
  const adminUserForCalls = await prisma.user.findFirst({ where: { role: 'ADMIN' } })

  if (adminUserForCalls && createdLeads.length > 0) {
    // Add call record for the first lead (Aziza Karimova)
    const lead1 = createdLeads.find(l => l.name === 'Aziza Karimova')
    if (lead1) {
      await prisma.callRecord.upsert({
        where: { id: 'test-call-record-1' },
        update: {},
        create: {
          id: 'test-call-record-1',
          leadId: lead1.id,
          userId: adminUserForCalls.id,
          startedAt: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
          endedAt: new Date(Date.now() - 30 * 1000), // 30 seconds ago
          duration: 90, // 1.5 minutes
          notes: 'Discussed IELTS preparation course. Student is interested.',
          recordingUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Test audio file
        },
      })
    }

    // Add call record for the second lead (Bobur Toshev)
    const lead2 = createdLeads.find(l => l.name === 'Bobur Toshev')
    if (lead2) {
      await prisma.callRecord.upsert({
        where: { id: 'test-call-record-2' },
        update: {},
        create: {
          id: 'test-call-record-2',
          leadId: lead2.id,
          userId: adminUserForCalls.id,
          startedAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
          endedAt: new Date(Date.now() - 3 * 60 * 1000), // 3 minutes ago
          duration: 120, // 2 minutes
          notes: 'Student wants to start with General English A1 level.',
          recordingUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Test audio file
        },
      })
    }
  }

  // Create sample students
  const studentUser1 = await prisma.user.upsert({
    where: { phone: '+998904444444' },
    update: {},
    create: {
      phone: '+998904444444',
      name: 'Aziza Karimova',
      email: '<EMAIL>',
      role: 'STUDENT',
      password: await bcrypt.hash('student123', 10),
    },
  })

  const student1 = await prisma.student.upsert({
    where: { userId: studentUser1.id },
    update: {},
    create: {
      userId: studentUser1.id,
      level: 'B1' as const,
      branch: 'Main Branch',
      emergencyContact: '+998901111111',
    },
  })

  // Create sample teacher
  const teacherUser = await prisma.user.upsert({
    where: { phone: '+998905555555' },
    update: {},
    create: {
      phone: '+998905555555',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      role: 'TEACHER',
      password: await bcrypt.hash('teacher123', 10),
    },
  })

  const teacher = await prisma.teacher.upsert({
    where: { userId: teacherUser.id },
    update: {},
    create: {
      userId: teacherUser.id,
      subject: 'English',
      experience: 5,
      salary: 5000000,
      branch: 'Main Branch',
    },
  })

  // Create sample groups
  const course1 = await prisma.course.findFirst({ where: { name: 'General English A1' } })
  if (course1) {
    await prisma.group.upsert({
      where: { name: 'A1-Morning-Group' },
      update: {},
      create: {
        name: 'A1-Morning-Group',
        courseId: course1.id,
        teacherId: teacher.id,
        capacity: 20,
        schedule: 'Mon, Wed, Fri - 9:00 AM',
        room: 'Room 101',
        branch: 'Main Branch',
        startDate: new Date('2024-01-15'),
        endDate: new Date('2024-04-15'),
      },
    })
  }

  // Create sample payments
  await prisma.payment.upsert({
    where: { id: 'sample-payment-1' },
    update: {},
    create: {
      id: 'sample-payment-1',
      studentId: student1.id,
      amount: 500000,
      method: 'CASH' as const,
      status: 'PAID' as const,
      description: 'Monthly tuition fee',
      paidDate: new Date(),
    },
  })

  console.log('🎉 Database seeded successfully!')
  console.log('\n📋 Test User Credentials:')
  console.log('   Admin: +998901234567 / admin123')
  console.log('   Manager: +998901234568 / manager123')
  console.log('   Reception: +998901234569 / reception123')
  console.log('   Cashier: +998901234570 / cashier123 (NEW ROLE)')
  console.log('   Teacher: +998905555555 / teacher123')
  console.log('   Student: +998904444444 / student123')
  console.log('\n🔐 Use these credentials to test role-based access control!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
