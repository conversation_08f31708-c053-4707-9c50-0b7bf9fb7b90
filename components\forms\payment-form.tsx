'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CreditCard, User, Calendar, DollarSign, FileText } from 'lucide-react'

const paymentSchema = z.object({
  studentId: z.string().min(1, 'Student is required'),
  amount: z.number().min(1, 'Amount must be greater than 0'),
  method: z.enum(['CASH', 'UZCARD', 'HUMO', 'PAYME', 'CLICK', 'BANK_TRANSFER']),
  status: z.enum(['PENDING', 'PAID', 'OVERDUE', 'REFUNDED']).default('PAID'),
  description: z.string().optional(),
  transactionId: z.string().optional(),
  dueDate: z.string().optional(),
  paidDate: z.string().optional(),
})

type PaymentFormData = z.infer<typeof paymentSchema>

interface PaymentFormProps {
  initialData?: Partial<PaymentFormData>
  onSubmit: (data: PaymentFormData) => Promise<void>
  onCancel?: () => void
  isEditing?: boolean
  preselectedStudentId?: string
}

interface Student {
  id: string
  user: {
    id: string
    name: string
    phone: string
  }
  enrollments: Array<{
    group: {
      course: {
        name: string
        price: number
      }
    }
  }>
}

const paymentMethods = [
  { value: 'CASH', label: 'Cash', icon: '💵' },
  { value: 'UZCARD', label: 'UzCard', icon: '💳' },
  { value: 'HUMO', label: 'Humo', icon: '💳' },
  { value: 'PAYME', label: 'PayMe', icon: '📱' },
  { value: 'CLICK', label: 'Click', icon: '📱' },
  { value: 'BANK_TRANSFER', label: 'Bank Transfer', icon: '🏦' },
]

const paymentStatuses = [
  { value: 'PAID', label: 'Paid', color: 'text-green-600' },
  { value: 'PENDING', label: 'Pending', color: 'text-yellow-600' },
  { value: 'OVERDUE', label: 'Overdue', color: 'text-red-600' },
  { value: 'REFUNDED', label: 'Refunded', color: 'text-blue-600' },
]

const commonAmounts = [
  { amount: 500000, label: '$40 (500K UZS)' },
  { amount: 625000, label: '$50 (625K UZS)' },
  { amount: 750000, label: '$60 (750K UZS)' },
  { amount: 875000, label: '$70 (875K UZS)' },
  { amount: 1000000, label: '$80 (1M UZS)' },
  { amount: 1250000, label: '$100 (1.25M UZS)' },
]

function PaymentForm({
  initialData,
  onSubmit,
  onCancel,
  isEditing = false,
  preselectedStudentId
}: PaymentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [students, setStudents] = useState<Student[]>([])
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      studentId: preselectedStudentId || initialData?.studentId || '',
      amount: initialData?.amount || 750000,
      method: initialData?.method || 'CASH',
      status: initialData?.status || 'PAID',
      description: initialData?.description || '',
      transactionId: initialData?.transactionId || '',
      dueDate: initialData?.dueDate || '',
      paidDate: initialData?.paidDate || new Date().toISOString().split('T')[0],
    },
  })

  const selectedStudentId = watch('studentId')
  const selectedMethod = watch('method')
  const selectedStatus = watch('status')
  const amount = watch('amount')

  useEffect(() => {
    fetchStudents()
  }, [])

  useEffect(() => {
    if (selectedStudentId) {
      const student = students.find(s => s.id === selectedStudentId)
      setSelectedStudent(student || null)
    }
  }, [selectedStudentId, students])

  useEffect(() => {
    // Auto-set paid date when status is PAID
    if (selectedStatus === 'PAID' && !watch('paidDate')) {
      setValue('paidDate', new Date().toISOString().split('T')[0])
    }
  }, [selectedStatus, setValue, watch])

  const fetchStudents = async () => {
    try {
      const response = await fetch('/api/students')
      const data = await response.json()
      setStudents(data.students || [])
    } catch (error) {
      console.error('Error fetching students:', error)
    }
  }

  const handleFormSubmit = async (data: PaymentFormData) => {
    setIsSubmitting(true)
    setError(null)

    try {
      await onSubmit(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const suggestedAmount = selectedStudent?.enrollments[0]?.group.course.price

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <CreditCard className="h-5 w-5 mr-2" />
          {isEditing ? 'Edit Payment' : 'Record New Payment'}
        </CardTitle>
        <CardDescription>
          {isEditing ? 'Update payment information' : 'Enter payment details to record a new transaction'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Student Selection */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <User className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Student Information</h3>
            </div>

            <div className="space-y-2">
              <Label htmlFor="studentId">Student *</Label>
              <Select
                value={selectedStudentId}
                onValueChange={(value) => setValue('studentId', value)}
                disabled={!!preselectedStudentId}
              >
                <SelectTrigger className={errors.studentId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select student" />
                </SelectTrigger>
                <SelectContent>
                  {students.map((student) => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.user.name} - {student.user.phone}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.studentId && (
                <p className="text-sm text-red-500">{errors.studentId.message}</p>
              )}
              {selectedStudent && (
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-sm font-medium">{selectedStudent.user.name}</p>
                  <p className="text-sm text-gray-600">{selectedStudent.user.phone}</p>
                  {selectedStudent.enrollments.length > 0 && (
                    <p className="text-sm text-gray-600">
                      Course: {selectedStudent.enrollments[0].group.course.name}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Payment Details */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <DollarSign className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Payment Details</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="amount">Amount (UZS) *</Label>
                <Input
                  id="amount"
                  type="number"
                  min="1"
                  step="1000"
                  {...register('amount', { valueAsNumber: true })}
                  className={errors.amount ? 'border-red-500' : ''}
                />
                {errors.amount && (
                  <p className="text-sm text-red-500">{errors.amount.message}</p>
                )}
                <p className="text-sm text-gray-600">
                  ≈ ${(amount / 12500).toFixed(0)} USD
                </p>
                {suggestedAmount && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setValue('amount', suggestedAmount)}
                  >
                    Use course price: {suggestedAmount.toLocaleString()} UZS
                  </Button>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="method">Payment Method *</Label>
                <Select
                  value={selectedMethod}
                  onValueChange={(value) => setValue('method', value as any)}
                >
                  <SelectTrigger className={errors.method ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select method" />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentMethods.map((method) => (
                      <SelectItem key={method.value} value={method.value}>
                        {method.icon} {method.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.method && (
                  <p className="text-sm text-red-500">{errors.method.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status *</Label>
                <Select
                  value={selectedStatus}
                  onValueChange={(value) => setValue('status', value as any)}
                >
                  <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentStatuses.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        <span className={status.color}>{status.label}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-red-500">{errors.status.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="transactionId">Transaction ID</Label>
                <Input
                  id="transactionId"
                  {...register('transactionId')}
                  placeholder="Enter transaction ID"
                />
              </div>
            </div>

            {/* Quick Amount Selection */}
            <div className="space-y-2">
              <Label>Quick Amount Selection</Label>
              <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
                {commonAmounts.map((preset) => (
                  <Button
                    key={preset.amount}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setValue('amount', preset.amount)}
                    className={amount === preset.amount ? 'bg-green-50 border-green-300' : ''}
                  >
                    {preset.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* Dates and Description */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Calendar className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Additional Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dueDate">Due Date</Label>
                <Input
                  id="dueDate"
                  type="date"
                  {...register('dueDate')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="paidDate">Paid Date</Label>
                <Input
                  id="paidDate"
                  type="date"
                  {...register('paidDate')}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Payment description or notes..."
                className="min-h-[80px]"
              />
            </div>
          </div>

          {/* Payment Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Payment Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Amount:</span>
                <span className="ml-2 font-medium">{amount.toLocaleString()} UZS</span>
              </div>
              <div>
                <span className="text-gray-600">USD Equivalent:</span>
                <span className="ml-2 font-medium">${(amount / 12500).toFixed(0)}</span>
              </div>
              <div>
                <span className="text-gray-600">Method:</span>
                <span className="ml-2 font-medium">
                  {paymentMethods.find(m => m.value === selectedMethod)?.label}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Status:</span>
                <span className={`ml-2 font-medium ${paymentStatuses.find(s => s.value === selectedStatus)?.color}`}>
                  {paymentStatuses.find(s => s.value === selectedStatus)?.label}
                </span>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Update Payment' : 'Record Payment'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default PaymentForm
export { PaymentForm }
