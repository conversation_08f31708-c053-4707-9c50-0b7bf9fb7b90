import { prisma } from './prisma'
import { Role } from '@prisma/client'

interface ActivityLogData {
  userId: string
  userRole: Role
  action: string
  resource: string
  resourceId?: string
  details?: any
  ipAddress?: string
  userAgent?: string
}

export class ActivityLogger {
  static async log(data: ActivityLogData) {
    try {
      await prisma.activityLog.create({
        data: {
          userId: data.userId,
          userRole: data.userRole,
          action: data.action,
          resource: data.resource,
          resourceId: data.resourceId,
          details: data.details,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
        },
      })
    } catch (error) {
      console.error('Failed to log activity:', error)
    }
  }

  static async logStudentCreated(userId: string, userRole: Role, studentId: string, studentData: any, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'CREATE',
      resource: 'student',
      resourceId: studentId,
      details: { studentData },
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static async logStudentUpdated(userId: string, userRole: Role, studentId: string, changes: any, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'UPDATE',
      resource: 'student',
      resourceId: studentId,
      details: { changes },
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static async logStudentDeleted(userId: string, userRole: Role, studentId: string, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'DELETE',
      resource: 'student',
      resourceId: studentId,
      details: {},
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static async logStudentStatusChanged(userId: string, userRole: Role, studentId: string, statusData: any, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'STATUS_CHANGE',
      resource: 'student',
      resourceId: studentId,
      details: { statusData },
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static async logDroppedStudentContacted(userId: string, userRole: Role, studentId: string, contactData: any, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'CONTACT',
      resource: 'dropped_student',
      resourceId: studentId,
      details: { contactData },
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static async logPaymentCreated(userId: string, userRole: Role, paymentId: string, paymentData: any, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'CREATE',
      resource: 'payment',
      resourceId: paymentId,
      details: { paymentData },
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static async logPaymentUpdated(userId: string, userRole: Role, paymentId: string, changes: any, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'UPDATE',
      resource: 'payment',
      resourceId: paymentId,
      details: { changes },
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static async logGroupCreated(userId: string, userRole: Role, groupId: string, groupData: any, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'CREATE',
      resource: 'group',
      resourceId: groupId,
      details: { groupData },
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static async logEnrollmentCreated(userId: string, userRole: Role, enrollmentId: string, enrollmentData: any, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'CREATE',
      resource: 'enrollment',
      resourceId: enrollmentId,
      details: { enrollmentData },
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static async logLeadContacted(userId: string, userRole: Role, leadId: string, contactDetails: any, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'CONTACT',
      resource: 'lead',
      resourceId: leadId,
      details: { contactDetails },
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static async logAssessmentCompleted(userId: string, userRole: Role, assessmentId: string, assessmentData: any, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'COMPLETE',
      resource: 'assessment',
      resourceId: assessmentId,
      details: { assessmentData },
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static async logLogin(userId: string, userRole: Role, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'LOGIN',
      resource: 'auth',
      details: {},
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static async logLogout(userId: string, userRole: Role, request?: Request) {
    await this.log({
      userId,
      userRole,
      action: 'LOGOUT',
      resource: 'auth',
      details: {},
      ipAddress: this.getIpAddress(request),
      userAgent: this.getUserAgent(request),
    })
  }

  static getIpAddress(request?: Request): string | undefined {
    if (!request) return undefined
    
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    
    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }
    
    if (realIp) {
      return realIp
    }
    
    return undefined
  }

  static getUserAgent(request?: Request): string | undefined {
    if (!request) return undefined
    return request.headers.get('user-agent') || undefined
  }

  // Get activity logs with filtering
  static async getActivityLogs(filters: {
    userId?: string
    userRole?: Role
    action?: string
    resource?: string
    startDate?: Date
    endDate?: Date
    page?: number
    limit?: number
  }) {
    const {
      userId,
      userRole,
      action,
      resource,
      startDate,
      endDate,
      page = 1,
      limit = 50
    } = filters

    const where: any = {}

    if (userId) where.userId = userId
    if (userRole) where.userRole = userRole
    if (action) where.action = action
    if (resource) where.resource = resource
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = startDate
      if (endDate) where.createdAt.lte = endDate
    }

    const [logs, total] = await Promise.all([
      prisma.activityLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.activityLog.count({ where }),
    ])

    return {
      logs,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    }
  }

  // Get KPI data for reception staff (new students added)
  static async getReceptionKPIs(startDate: Date, endDate: Date) {
    const newStudents = await prisma.activityLog.findMany({
      where: {
        action: 'CREATE',
        resource: 'student',
        userRole: 'RECEPTION',
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    // Group by user
    const userStats = newStudents.reduce((acc, log) => {
      const userId = log.userId
      const userName = log.user.name
      
      if (!acc[userId]) {
        acc[userId] = {
          userId,
          userName,
          studentsAdded: 0,
        }
      }
      
      acc[userId].studentsAdded++
      return acc
    }, {} as Record<string, { userId: string; userName: string; studentsAdded: number }>)

    return Object.values(userStats)
  }

  // Get KPI data for call centre staff (students contacted)
  static async getCallCentreKPIs(startDate: Date, endDate: Date) {
    const contactedLeads = await prisma.activityLog.findMany({
      where: {
        action: 'CONTACT',
        resource: 'lead',
        userRole: { in: ['RECEPTION', 'MANAGER'] }, // Call centre roles
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    // Group by user
    const userStats = contactedLeads.reduce((acc, log) => {
      const userId = log.userId
      const userName = log.user.name
      
      if (!acc[userId]) {
        acc[userId] = {
          userId,
          userName,
          leadsContacted: 0,
        }
      }
      
      acc[userId].leadsContacted++
      return acc
    }, {} as Record<string, { userId: string; userName: string; leadsContacted: number }>)

    return Object.values(userStats)
  }
}
