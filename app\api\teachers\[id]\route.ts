import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const updateTeacherSchema = z.object({
  subject: z.string().min(1).optional(),
  experience: z.number().min(0).optional(),
  salary: z.number().min(0).optional(),
  branch: z.string().min(1).optional(),
  photoUrl: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const teacher = await prisma.teacher.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
            role: true,
            createdAt: true,
          },
        },
        groups: {
          include: {
            course: {
              select: {
                id: true,
                name: true,
                level: true,
                duration: true,
                price: true,
              },
            },
            enrollments: {
              include: {
                student: {
                  include: {
                    user: {
                      select: {
                        name: true,
                      },
                    },
                  },
                },
              },
            },
            _count: {
              select: {
                enrollments: true,
                classes: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        classes: {
          include: {
            group: {
              select: {
                id: true,
                name: true,
              },
            },
            _count: {
              select: {
                attendances: true,
              },
            },
          },
          orderBy: { date: 'desc' },
          take: 20,
        },
        _count: {
          select: {
            groups: true,
            classes: true,
          },
        },
      },
    })

    if (!teacher) {
      return NextResponse.json(
        { error: 'Teacher not found' },
        { status: 404 }
      )
    }

    // Calculate total students across all groups
    const totalStudents = teacher.groups.reduce(
      (total, group) => total + group._count.enrollments,
      0
    )

    return NextResponse.json({
      ...teacher,
      totalStudents,
    })
  } catch (error) {
    console.error('Error fetching teacher:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const validatedData = updateTeacherSchema.parse(body)

    // Check if teacher exists
    const existingTeacher = await prisma.teacher.findUnique({
      where: { id },
    })

    if (!existingTeacher) {
      return NextResponse.json(
        { error: 'Teacher not found' },
        { status: 404 }
      )
    }

    const teacher = await prisma.teacher.update({
      where: { id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
            role: true,
          },
        },
        _count: {
          select: {
            groups: true,
            classes: true,
          },
        },
      },
    })

    return NextResponse.json(teacher)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating teacher:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    // Check if teacher exists
    const existingTeacher = await prisma.teacher.findUnique({
      where: { id },
      include: {
        groups: true,
      },
    })

    if (!existingTeacher) {
      return NextResponse.json(
        { error: 'Teacher not found' },
        { status: 404 }
      )
    }

    // Check if teacher has active groups
    if (existingTeacher.groups.length > 0) {
      const activeGroups = existingTeacher.groups.filter(group => group.isActive)
      
      if (activeGroups.length > 0) {
        return NextResponse.json(
          { 
            error: 'Cannot delete teacher with active groups',
            details: `Teacher has ${activeGroups.length} active group(s)`
          },
          { status: 400 }
        )
      }
    }



    // Delete teacher
    await prisma.teacher.delete({
      where: { id },
    })

    return NextResponse.json({
      message: 'Teacher deleted successfully',
      deletedId: id
    })
  } catch (error) {
    console.error('Error deleting teacher:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
