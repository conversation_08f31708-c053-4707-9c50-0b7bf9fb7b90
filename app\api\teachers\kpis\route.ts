import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only allow certain roles to view teacher KPIs
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get all teachers with their groups and student payments
    const teachers = await prisma.teacher.findMany({
      include: {
        user: {
          select: {
            name: true,
          },
        },
        groups: {
          include: {
            enrollments: {
              include: {
                student: {
                  include: {
                    payments: {
                      where: {
                        status: {
                          in: ['PAID', 'REFUNDED']
                        }
                      },
                      select: {
                        amount: true,
                        status: true,
                        createdAt: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        classes: true,
        _count: {
          select: {
            groups: true,
            classes: true,
          },
        },
      },
    })

    // Calculate KPIs
    const totalTeachers = teachers.length
    const totalGroups = teachers.reduce((sum, teacher) => sum + teacher._count.groups, 0)
    const totalClasses = teachers.reduce((sum, teacher) => sum + teacher._count.classes, 0)

    // Calculate salaries for each teacher based on 50% of student payments minus refunds
    const teachersWithCalculatedSalaries = teachers.map(teacher => {
      let totalPayments = 0
      let totalRefunds = 0

      // Get payments from enrolled students
      teacher.groups.forEach(group => {
        // Get payments from historical enrollments
        group.enrollments.forEach(enrollment => {
          enrollment.student.payments.forEach(payment => {
            if (payment.status === 'PAID') {
              totalPayments += Number(payment.amount)
            } else if (payment.status === 'REFUNDED') {
              totalRefunds += Number(payment.amount)
            }
          })
        })
      })

      // Calculate net payments (payments minus refunds)
      const netPayments = totalPayments - totalRefunds
      
      // Teacher gets 50% of net payments
      const calculatedSalary = Math.max(0, netPayments * 0.5)

      return {
        ...teacher,
        calculatedSalary,
        totalPayments,
        totalRefunds,
        netPayments,
      }
    })

    // Calculate average salary
    const avgSalary = teachersWithCalculatedSalaries.length > 0
      ? teachersWithCalculatedSalaries.reduce((sum, teacher) => sum + teacher.calculatedSalary, 0) / teachersWithCalculatedSalaries.length
      : 0

    // Update teacher salaries in database (optional - you might want to do this periodically)
    for (const teacher of teachersWithCalculatedSalaries) {
      await prisma.teacher.update({
        where: { id: teacher.id },
        data: { salary: teacher.calculatedSalary },
      })
    }

    const kpis = {
      totalTeachers,
      totalGroups,
      totalClasses,
      avgSalary: Math.round(avgSalary),
      totalStudents: teachersWithCalculatedSalaries.reduce((sum, teacher) => {
        return sum + teacher.groups.reduce((groupSum, group) => {
          return groupSum + group.enrollments.length
        }, 0)
      }, 0),
      totalPayments: teachersWithCalculatedSalaries.reduce((sum, teacher) => sum + teacher.totalPayments, 0),
      totalRefunds: teachersWithCalculatedSalaries.reduce((sum, teacher) => sum + teacher.totalRefunds, 0),
      teacherBreakdown: teachersWithCalculatedSalaries.map(teacher => ({
        id: teacher.id,
        name: teacher.user.name,
        calculatedSalary: teacher.calculatedSalary,
        totalPayments: teacher.totalPayments,
        totalRefunds: teacher.totalRefunds,
        netPayments: teacher.netPayments,
        groups: teacher._count.groups,
        classes: teacher._count.classes,
      })),
    }

    return NextResponse.json(kpis)
  } catch (error) {
    console.error('Error fetching teacher KPIs:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
