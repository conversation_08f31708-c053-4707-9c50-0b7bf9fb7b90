import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LeadForm } from '@/components/forms/lead-form'
import { Phone, MapPin, Clock, Users, Award, BookOpen } from 'lucide-react'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">Innovative Centre</h1>
            </div>
            <Link href="/auth/signin">
              <Button variant="outline">Staff Login</Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">
                Master English at Uzbekistan&apos;s Leading Language Center
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Join 4,000+ students across our 2 branches. From General English to IELTS preparation, 
                we offer comprehensive courses for all ages and levels.
              </p>
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-blue-600 mr-2" />
                  <span className="text-gray-700">4,000+ Students</span>
                </div>
                <div className="flex items-center">
                  <Award className="h-5 w-5 text-blue-600 mr-2" />
                  <span className="text-gray-700">Expert Teachers</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-blue-600 mr-2" />
                  <span className="text-gray-700">2 Branches</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-600 mr-2" />
                  <span className="text-gray-700">Flexible Schedule</span>
                </div>
              </div>
            </div>
            <div>
              <Card className="w-full max-w-md mx-auto">
                <CardHeader>
                  <CardTitle>Start Your English Journey</CardTitle>
                  <CardDescription>
                    Fill out the form below and we&apos;ll contact you within 24 hours
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <LeadForm />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Courses Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Our Courses</h3>
            <p className="text-xl text-gray-600">Choose from our comprehensive range of English programs</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {courses.map((course, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-xl">{course.name}</CardTitle>
                  <CardDescription>{course.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">Duration: {course.duration}</p>
                    <p className="text-sm text-gray-600">Level: {course.level}</p>
                    <p className="text-lg font-semibold text-blue-600">{course.price}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Contact Us</h3>
            <p className="text-xl text-gray-600">Visit our branches or get in touch</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Main Branch</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-blue-600 mr-3" />
                  <span>Tashkent, Uzbekistan</span>
                </div>
                <div className="flex items-center">
                  <Phone className="h-5 w-5 text-blue-600 mr-3" />
                  <span>+998 90 123 45 67</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-600 mr-3" />
                  <span>Mon-Sat: 8:00 AM - 8:00 PM</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Second Branch</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-blue-600 mr-3" />
                  <span>Tashkent, Uzbekistan</span>
                </div>
                <div className="flex items-center">
                  <Phone className="h-5 w-5 text-blue-600 mr-3" />
                  <span>+998 90 123 45 68</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-600 mr-3" />
                  <span>Mon-Sat: 8:00 AM - 8:00 PM</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p>&copy; 2024 Innovative Centre. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

const courses = [
  {
    name: "General English",
    description: "Complete English course from A1 to C2 levels",
    duration: "3-4 months per level",
    level: "A1 - C2",
    price: "From 500,000 UZS/month"
  },
  {
    name: "IELTS Preparation",
    description: "Intensive IELTS preparation for all band scores",
    duration: "2-3 months",
    level: "5.5 - 7.0+",
    price: "From 800,000 UZS/month"
  },
  {
    name: "SAT Preparation",
    description: "Comprehensive SAT preparation course",
    duration: "3-4 months",
    level: "Intermediate+",
    price: "From 700,000 UZS/month"
  },
  {
    name: "Kids English",
    description: "Fun and engaging English for children",
    duration: "6 months",
    level: "Ages 6-12",
    price: "From 400,000 UZS/month"
  },
  {
    name: "Business English",
    description: "Professional English for business communication",
    duration: "2-3 months",
    level: "B1+",
    price: "From 600,000 UZS/month"
  },
  {
    name: "Math Courses",
    description: "Mathematics courses in English",
    duration: "4-6 months",
    level: "All levels",
    price: "From 450,000 UZS/month"
  }
]
