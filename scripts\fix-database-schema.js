const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function fixDatabaseSchema() {
  console.log('🔧 Fixing Database Schema Issues...')
  console.log('=' .repeat(50))

  try {
    // Check if students table exists and has status column
    console.log('\n📋 Checking students table structure...')
    
    try {
      const result = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'students' 
        AND table_schema = 'public'
        ORDER BY ordinal_position;
      `
      
      console.log('✅ Students table columns:')
      result.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`)
      })
      
      // Check if status column exists
      const hasStatusColumn = result.some(col => col.column_name === 'status')
      
      if (!hasStatusColumn) {
        console.log('\n⚠️  Status column missing from students table')
        console.log('🔧 Adding status column...')
        
        // Add status column with default value
        await prisma.$executeRaw`
          ALTER TABLE students 
          ADD COLUMN status "StudentStatus" DEFAULT 'ACTIVE'::\"StudentStatus\"
        `
        
        console.log('✅ Status column added successfully')
      } else {
        console.log('✅ Status column exists')
      }
      
    } catch (error) {
      console.log('❌ Error checking students table:', error.message)
      
      // If the table doesn't exist, we need to run prisma db push
      if (error.message.includes('relation "students" does not exist')) {
        console.log('\n🔧 Students table does not exist. Database schema needs to be created.')
        console.log('Please run: npx prisma db push')
        return
      }
    }

    // Check if Level enum has correct values
    console.log('\n📋 Checking Level enum values...')
    
    try {
      const enumValues = await prisma.$queryRaw`
        SELECT enumlabel as value
        FROM pg_enum 
        WHERE enumtypid = (
          SELECT oid 
          FROM pg_type 
          WHERE typname = 'Level'
        )
        ORDER BY enumlabel;
      `
      
      console.log('✅ Current Level enum values:')
      enumValues.forEach(val => {
        console.log(`  - ${val.value}`)
      })
      
      const expectedValues = ['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']
      const currentValues = enumValues.map(v => v.value)
      
      // Check for old values that need to be removed
      const oldValues = ['C1', 'C2', 'IELTS_5_5', 'IELTS_6_0', 'IELTS_6_5', 'IELTS_7_0']
      const hasOldValues = oldValues.some(val => currentValues.includes(val))
      
      if (hasOldValues) {
        console.log('\n⚠️  Old Level enum values detected')
        console.log('🔧 Database schema needs to be updated with new Level enum')
        console.log('Please run: npx prisma db push')
      } else {
        console.log('✅ Level enum values are up to date')
      }
      
    } catch (error) {
      console.log('❌ Error checking Level enum:', error.message)
    }

    // Test basic queries
    console.log('\n🧪 Testing basic database queries...')
    
    try {
      const studentCount = await prisma.student.count()
      console.log(`✅ Students count: ${studentCount}`)
      
      const userCount = await prisma.user.count()
      console.log(`✅ Users count: ${userCount}`)
      
      // Test the problematic query from the API
      try {
        const statusCounts = await prisma.student.groupBy({
          by: ['status'],
          _count: {
            status: true,
          },
        })
        console.log('✅ Status grouping query works')
        console.log('📊 Status counts:', statusCounts)
      } catch (statusError) {
        console.log('❌ Status grouping query failed:', statusError.message)
        
        if (statusError.message.includes('column "students.status" does not exist')) {
          console.log('🔧 Need to add status column to students table')
          console.log('Please run: npx prisma db push')
        }
      }
      
    } catch (error) {
      console.log('❌ Error testing queries:', error.message)
    }

    console.log('\n' + '='.repeat(50))
    console.log('📊 SUMMARY')
    console.log('='.repeat(50))
    
    console.log('\n✅ Database schema check completed')
    console.log('\n📋 Next steps:')
    console.log('1. If schema issues were found, run: npx prisma db push')
    console.log('2. Restart the development server: npm run dev')
    console.log('3. Test student creation and listing')

  } catch (error) {
    console.error('❌ Fatal error:', error)
    console.log('\n🔧 Recommended actions:')
    console.log('1. Check database connection')
    console.log('2. Run: npx prisma db push')
    console.log('3. Run: npx prisma generate')
  } finally {
    await prisma.$disconnect()
  }
}

// Run the fix
fixDatabaseSchema().catch(error => {
  console.error('Script failed:', error)
  process.exit(1)
})
