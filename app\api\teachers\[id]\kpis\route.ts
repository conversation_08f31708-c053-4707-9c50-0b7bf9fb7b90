import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only admins, managers, and the teacher themselves can view KPIs
    if (!session.user.role || (!['ADMIN', 'MANAGER'].includes(session.user.role) && session.user.id !== id)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get teacher info
    const teacher = await prisma.teacher.findUnique({
      where: { id },
      include: { user: true }
    })

    if (!teacher) {
      return NextResponse.json({ error: 'Teacher not found' }, { status: 404 })
    }

    // Get teacher's groups
    const groups = await prisma.group.findMany({
      where: { teacherId: id },
      include: {
        enrollments: {
          include: {
            student: {
              include: {
                user: true,
                payments: true
              }
            }
          }
        }
      }
    })

    // Calculate KPIs
    const kpis = await calculateTeacherKPIs(id, startDate, groups)

    return NextResponse.json({
      teacher: {
        id: teacher.id,
        name: teacher.user.name,
        email: teacher.user.email,
      },
      period: `${period} days`,
      kpis
    })

  } catch (error) {
    console.error('Error fetching teacher KPIs:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function calculateTeacherKPIs(teacherId: string, startDate: Date, groups: any[]) {
  const now = new Date()
  
  // 1. New Students Acquired
  const newStudents = await prisma.enrollment.count({
    where: {
      group: { teacherId },
      createdAt: { gte: startDate }
    }
  })

  // 2. Student Payments Collected
  const paymentsData = await prisma.payment.aggregate({
    where: {
      student: {
        enrollments: {
          some: {
            group: { teacherId }
          }
        }
      },
      createdAt: { gte: startDate },
      status: 'PAID'
    },
    _sum: { amount: true },
    _count: true
  })

  // 3. Retention Rate
  const totalStudents = await prisma.enrollment.count({
    where: {
      group: { teacherId },
      status: { in: ['ACTIVE', 'COMPLETED', 'DROPPED'] }
    }
  })

  const activeStudents = await prisma.enrollment.count({
    where: {
      group: { teacherId },
      status: 'ACTIVE'
    }
  })

  const retentionRate = totalStudents > 0 ? (activeStudents / totalStudents) * 100 : 0

  // 4. Progress Rate (students who advanced levels)
  const progressData = await prisma.student.findMany({
    where: {
      enrollments: {
        some: {
          group: { teacherId }
        }
      }
    },
    include: {
      assessments: {
        where: {
          completedAt: { gte: startDate },
          passed: true,
          type: { in: ['LEVEL_TEST', 'FINAL_EXAM'] }
        },
        orderBy: { completedAt: 'desc' }
      }
    }
  })

  const studentsWithProgress = progressData.filter(student => 
    student.assessments.length > 0
  ).length

  const progressRate = totalStudents > 0 ? (studentsWithProgress / totalStudents) * 100 : 0

  // 5. Average Test Scores
  const testScores = await prisma.assessment.aggregate({
    where: {
      student: {
        enrollments: {
          some: {
            group: { teacherId }
          }
        }
      },
      completedAt: { gte: startDate },
      score: { not: null }
    },
    _avg: { score: true },
    _count: true
  })

  // 6. Class Attendance (if we have class data)
  const classAttendance = await prisma.class.aggregate({
    where: {
      group: { teacherId },
      date: { gte: startDate }
    },
    _count: true
  })

  return {
    newStudents: {
      count: newStudents,
      label: 'New Students Acquired'
    },
    payments: {
      totalAmount: paymentsData._sum.amount || 0,
      count: paymentsData._count,
      label: 'Payments Collected'
    },
    retention: {
      rate: Math.round(retentionRate * 100) / 100,
      activeStudents,
      totalStudents,
      label: 'Retention Rate (%)'
    },
    progress: {
      rate: Math.round(progressRate * 100) / 100,
      studentsWithProgress,
      totalStudents,
      label: 'Progress Rate (%)'
    },
    testPerformance: {
      averageScore: testScores._avg.score ? Math.round(testScores._avg.score * 100) / 100 : 0,
      testsCompleted: testScores._count,
      label: 'Average Test Score'
    },
    classActivity: {
      classesHeld: classAttendance._count,
      label: 'Classes Held'
    }
  }
}
